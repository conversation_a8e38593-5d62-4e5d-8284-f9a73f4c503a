package br.com.alice.ehr.utils

import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService

object FeatureFlags {
    
    /**
     * Verifica se o processamento de appointments deve ser pulado baseado no feature flag.
     * Quando retorna true, o processamento deve ser interrompido.
     */
    fun shouldSkipAppointmentProcessing(): Boolean {
        return FeatureService.get(
            namespace = FeatureNamespace.EHR,
            key = "send_process_appointment",
            defaultValue = false
        )
    }
}
